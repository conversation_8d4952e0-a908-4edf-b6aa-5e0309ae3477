import React, { useEffect, useState, useCallback } from "react";
import { fetchChatHistory } from "../../../backend/services/historyService";
import "./HistorySidebar.css";

const History = ({ isLoggedIn, session, onChatSelect, selectedConversationId }) => {
  const [conversations, setConversations] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredConversations, setFilteredConversations] = useState([]);

  useEffect(() => {
    async function loadHistory() {
      if (isLoggedIn && session?.user?.id) {
        setLoading(true);
        setError(null);
        try {
          const records = await fetchChatHistory(session.user.id);
          // Group messages into conversations like ChatGPT
          const conversationList = groupMessagesIntoConversations(records);
          setConversations(conversationList);
          setFilteredConversations(conversationList);
        } catch (error) {
          console.error("Failed to load chat history:", error);
          setError("Failed to load chat history. Please try again.");
        } finally {
          setLoading(false);
        }
      } else {
        setConversations([]);
        setFilteredConversations([]);
      }
    }
    loadHistory();
  }, [isLoggedIn, session, groupMessagesIntoConversations]);

  // Group messages into conversations like ChatGPT
  const groupMessagesIntoConversations = useCallback((messages) => {
    if (!messages || messages.length === 0) return [];

    const conversations = [];
    let currentConversation = null;

    // Sort messages by timestamp
    const sortedMessages = [...messages].sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp));

    for (let i = 0; i < sortedMessages.length; i++) {
      const message = sortedMessages[i];

      // Start a new conversation if this is a user message and we don't have a current conversation
      // or if there's a significant time gap (more than 1 hour)
      if (message.sender === 'user') {
        const timeDiff = currentConversation ?
          (new Date(message.timestamp) - new Date(currentConversation.lastActivity)) / (1000 * 60 * 60) :
          Infinity;

        if (!currentConversation || timeDiff > 1) {
          // Save previous conversation if exists
          if (currentConversation) {
            conversations.push(currentConversation);
          }

          // Start new conversation with a meaningful title
          const title = generateConversationTitle(message.text);
          currentConversation = {
            id: `conversation_${message.id}`,
            title: title,
            startTime: message.timestamp,
            lastActivity: message.timestamp,
            messages: [message],
            messageCount: 1
          };
        } else {
          // Add to current conversation
          currentConversation.messages.push(message);
          currentConversation.lastActivity = message.timestamp;
          currentConversation.messageCount++;
        }
      } else if (currentConversation) {
        // Add bot message to current conversation
        currentConversation.messages.push(message);
        currentConversation.lastActivity = message.timestamp;
        currentConversation.messageCount++;
      }
    }

    // Add the last conversation
    if (currentConversation) {
      conversations.push(currentConversation);
    }

    return conversations.reverse(); // Most recent first
  }, []);

  // Generate meaningful conversation titles like ChatGPT
  const generateConversationTitle = (firstMessage) => {
    // Remove common question words and get the main topic
    const cleanMessage = firstMessage
      .replace(/^(what|how|why|when|where|who|can|could|would|should|is|are|do|does|did|tell|explain|help)\s+/i, '')
      .replace(/\?+$/, '')
      .trim();

    // Take first 40 characters and add ellipsis if needed
    if (cleanMessage.length > 40) {
      return cleanMessage.substring(0, 40).trim() + '...';
    }

    return cleanMessage || 'New Conversation';
  };

  // Filter conversations based on search term
  useEffect(() => {
    if (searchTerm.trim() === "") {
      setFilteredConversations(conversations);
    } else {
      const filtered = conversations.filter(conversation =>
        conversation.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        conversation.messages.some(msg =>
          msg.text.toLowerCase().includes(searchTerm.toLowerCase())
        )
      );
      setFilteredConversations(filtered);
    }
  }, [searchTerm, conversations]);

  const formatTime = (timestamp) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now - date) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else if (diffInHours < 168) { // 7 days
      return date.toLocaleDateString([], { weekday: 'short', hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit' });
    }
  };

  const handleConversationClick = (conversation) => {
    if (onChatSelect) {
      onChatSelect(conversation);
    }
  };

  return (
    <div className="history-container">
      <div className="history-header">
        <div className="header-content">
          <h2 className="history-title">
            <i className="fas fa-history"></i>
            Chat History
          </h2>
          <p className="history-subtitle">Your conversation sessions with Recallo</p>
        </div>

        {isLoggedIn && conversations.length > 0 && (
          <div className="search-container">
            <div className="search-input-wrapper">
              <i className="fas fa-search search-icon"></i>
              <input
                type="text"
                className="search-input"
                placeholder="Search conversations..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
              {searchTerm && (
                <button
                  className="clear-search"
                  onClick={() => setSearchTerm("")}
                  aria-label="Clear search"
                >
                  <i className="fas fa-times"></i>
                </button>
              )}
            </div>
          </div>
        )}
      </div>

      <div className="history-content">
        {!isLoggedIn ? (
          <div className="auth-prompt">
            <div className="auth-icon">
              <i className="fas fa-lock"></i>
            </div>
            <h3>Sign In Required</h3>
            <p>Please sign in to access your personalized chat history and continue your learning journey.</p>
            <div className="auth-features">
              <div className="feature-item">
                <i className="fas fa-save"></i>
                <span>Save conversations</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-search"></i>
                <span>Search history</span>
              </div>
              <div className="feature-item">
                <i className="fas fa-sync"></i>
                <span>Sync across devices</span>
              </div>
            </div>
          </div>
        ) : loading ? (
          <div className="loading-container">
            <div className="loading-spinner">
              <div className="spinner"></div>
            </div>
            <p className="loading-text">Loading your conversations...</p>
          </div>
        ) : error ? (
          <div className="error-container">
            <div className="error-icon">
              <i className="fas fa-exclamation-triangle"></i>
            </div>
            <h3>Oops! Something went wrong</h3>
            <p>{error}</p>
            <button
              className="retry-button"
              onClick={() => window.location.reload()}
            >
              <i className="fas fa-redo"></i>
              Try Again
            </button>
          </div>
        ) : filteredConversations.length === 0 ? (
          searchTerm ? (
            <div className="no-results">
              <div className="no-results-icon">
                <i className="fas fa-search"></i>
              </div>
              <h3>No matches found</h3>
              <p>No conversations match "{searchTerm}". Try a different search term.</p>
              <button
                className="clear-search-button"
                onClick={() => setSearchTerm("")}
              >
                Clear Search
              </button>
            </div>
          ) : (
            <div className="empty-state">
              <div className="empty-icon">
                <i className="fas fa-comments"></i>
              </div>
              <h3>No conversations yet</h3>
              <p>Start chatting with Recallo to build your learning history!</p>
              <div className="empty-features">
                <div className="feature-tip">
                  <i className="fas fa-lightbulb"></i>
                  <span>Ask questions about any topic</span>
                </div>
                <div className="feature-tip">
                  <i className="fas fa-brain"></i>
                  <span>Get personalized learning assistance</span>
                </div>
                <div className="feature-tip">
                  <i className="fas fa-chart-line"></i>
                  <span>Track your learning progress</span>
                </div>
              </div>
            </div>
          )
        ) : (
          <div className="chat-sessions-list">
            <div className="results-info">
              {searchTerm && (
                <p className="search-results">
                  Found {filteredConversations.length} conversation{filteredConversations.length !== 1 ? 's' : ''}
                  matching "{searchTerm}"
                </p>
              )}
            </div>

            {filteredConversations.map((conversation) => (
              <div
                key={conversation.id}
                className={`conversation-item ${selectedConversationId === conversation.id ? 'active' : ''}`}
                onClick={() => handleConversationClick(conversation)}
              >
                <div className="conversation-content">
                  <div className="conversation-icon">
                    <i className="fas fa-message"></i>
                  </div>
                  <div className="conversation-info">
                    <h4 className="conversation-title">{conversation.title}</h4>
                    <span className="conversation-time">{formatTime(conversation.lastActivity)}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default History;
