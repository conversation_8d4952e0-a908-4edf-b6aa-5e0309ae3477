-- Insert a test user for testing (with dummy password hash)
-- The user_id column already exists in chat_logs table, so we just need to add the test user

INSERT INTO users (user_id, email, name, password_hash)
VALUES ('a0929f2e-35a0-4544-aac0-f406d17f77d7', '<EMAIL>', 'Test User', 'dummy_hash_for_testing')
ON CONFLICT (user_id) DO NOTHING;

-- Verify the test user was created
SELECT user_id, email, name FROM users WHERE user_id = 'a0929f2e-35a0-4544-aac0-f406d17f77d7';
