/* ChatGPT-style History Sidebar */
.history-container {
  height: 100vh;
  width: 280px;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  border-right: 1px solid var(--cs-border);
  font-family: 'Raleway', -apple-system, BlinkMacSystemFont, sans-serif;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  position: relative;
}

/* Header Styles */
.history-header {
  flex-shrink: 0;
  padding: 1rem;
  border-bottom: 1px solid var(--cs-border);
}

.header-content {
  margin-bottom: 1rem;
}

.history-title {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--cs-white);
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.history-title i {
  color: var(--cs-primary);
  font-size: 1rem;
}

.history-subtitle {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.85rem;
  margin: 0;
  font-weight: 400;
}

/* Search Styles */
.search-container {
  padding: 0 1rem 1rem 1rem;
  border-bottom: 1px solid var(--cs-border);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--cs-border);
  border-radius: 8px;
  color: var(--cs-white);
  font-size: 0.9rem;
  font-family: inherit;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--cs-primary);
  background: rgba(255, 255, 255, 0.08);
  box-shadow: 0 0 0 2px rgba(237, 180, 55, 0.2);
}

.search-input::placeholder {
  color: rgba(255, 255, 255, 0.4);
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--cs-primary);
  font-size: 0.9rem;
  pointer-events: none;
}

.clear-search {
  position: absolute;
  right: 0.75rem;
  background: none;
  border: none;
  color: rgba(255, 255, 255, 0.6);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: var(--cs-white);
  background: rgba(255, 255, 255, 0.1);
}

/* Conversations List */
.chat-sessions-list {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.conversation-item {
  padding: 0.75rem 1rem;
  margin: 0.25rem 0.5rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
  position: relative;
}

.conversation-item:hover {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(237, 180, 55, 0.3);
}

.conversation-item.active {
  background: rgba(237, 180, 55, 0.1);
  border-color: var(--cs-primary);
}

.conversation-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.conversation-icon {
  width: 32px;
  height: 32px;
  background: var(--cs-primary);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.conversation-icon i {
  color: var(--cs-black);
  font-size: 0.9rem;
}

.conversation-info {
  flex: 1;
  min-width: 0;
}

.conversation-title {
  color: var(--cs-white);
  font-size: 0.9rem;
  font-weight: 500;
  margin: 0 0 0.25rem 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.3;
}

.conversation-time {
  color: rgba(255, 255, 255, 0.5);
  font-size: 0.75rem;
  font-weight: 400;
}

/* Auth Prompt */
.auth-prompt {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
}

.auth-icon {
  width: 60px;
  height: 60px;
  background: rgba(237, 180, 55, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.auth-icon i {
  color: var(--cs-primary);
  font-size: 1.5rem;
}

.auth-prompt h3 {
  color: var(--cs-white);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.auth-prompt p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0 0 1.5rem 0;
  line-height: 1.5;
}

/* Empty State */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
}

.empty-icon {
  width: 60px;
  height: 60px;
  background: rgba(237, 180, 55, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
}

.empty-icon i {
  color: var(--cs-primary);
  font-size: 1.5rem;
}

.empty-state h3 {
  color: var(--cs-white);
  font-size: 1.1rem;
  font-weight: 600;
  margin: 0 0 0.5rem 0;
}

.empty-state p {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0;
  line-height: 1.5;
}

/* Loading State */
.loading-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid rgba(255, 255, 255, 0.1);
  border-top: 3px solid var(--cs-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Scrollbar */
.chat-sessions-list::-webkit-scrollbar {
  width: 4px;
}

.chat-sessions-list::-webkit-scrollbar-track {
  background: transparent;
}

.chat-sessions-list::-webkit-scrollbar-thumb {
  background: rgba(237, 180, 55, 0.3);
  border-radius: 2px;
}

.chat-sessions-list::-webkit-scrollbar-thumb:hover {
  background: var(--cs-primary);
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .history-container {
    width: 100%;
    height: 100vh;
  }
  
  .conversation-item {
    padding: 1rem;
    margin: 0.25rem;
  }
  
  .conversation-title {
    font-size: 1rem;
  }
  
  .conversation-time {
    font-size: 0.8rem;
  }
}
