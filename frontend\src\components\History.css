/* History Component Styles */
.history-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 1rem;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
}

/* Header Styles */
.history-header {
  max-width: 800px;
  margin: 0 auto 2rem;
  text-align: center;
}

.header-content {
  margin-bottom: 2rem;
}

.history-title {
  color: white;
  font-size: 2.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.75rem;
}

.history-title i {
  font-size: 2.2rem;
  opacity: 0.9;
}

.history-subtitle {
  color: rgba(255, 255, 255, 0.8);
  font-size: 1.1rem;
  margin: 0;
  font-weight: 300;
}

/* Search Container */
.search-container {
  max-width: 500px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  padding: 1rem 1rem 1rem 3rem;
  border: none;
  border-radius: 50px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  font-size: 1rem;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  background: white;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.search-icon {
  position: absolute;
  left: 1rem;
  color: #666;
  z-index: 2;
}

.clear-search {
  position: absolute;
  right: 1rem;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #333;
}

/* Content Container */
.history-content {
  max-width: 800px;
  margin: 0 auto;
}

/* Auth Prompt */
.auth-prompt {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.auth-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1.5rem;
}

.auth-prompt h3 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.auth-prompt p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.auth-features {
  display: flex;
  justify-content: center;
  gap: 2rem;
  flex-wrap: wrap;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #667eea;
  font-weight: 500;
}

.feature-item i {
  font-size: 1.2rem;
}

/* Loading Styles */
.loading-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.loading-spinner {
  margin-bottom: 1.5rem;
}

.spinner {
  width: 50px;
  height: 50px;
  border: 4px solid rgba(102, 126, 234, 0.2);
  border-left: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

/* Error Styles */
.error-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 3rem;
  color: #e74c3c;
  margin-bottom: 1.5rem;
}

.error-container h3 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.error-container p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

.retry-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.retry-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Empty State */
.empty-state, .no-results {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 3rem 2rem;
  text-align: center;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
}

.empty-icon, .no-results-icon {
  font-size: 3rem;
  color: #667eea;
  margin-bottom: 1.5rem;
}

.empty-state h3, .no-results h3 {
  color: #333;
  font-size: 1.8rem;
  margin-bottom: 1rem;
  font-weight: 600;
}

.empty-state p, .no-results p {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
  line-height: 1.6;
}

.empty-features {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
}

.feature-tip {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: #667eea;
  font-weight: 500;
  font-size: 1rem;
}

.feature-tip i {
  font-size: 1.2rem;
  width: 20px;
  text-align: center;
}

.clear-search-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 50px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.clear-search-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Chat History List */
.chat-history-list {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
  max-height: 70vh;
  overflow-y: auto;
}

.results-info {
  margin-bottom: 1.5rem;
  text-align: center;
}

.search-results {
  color: #667eea;
  font-weight: 500;
  margin: 0;
  font-size: 0.95rem;
}

/* Message Bubbles */
.message-bubble {
  margin-bottom: 1.5rem;
  animation: fadeInUp 0.5s ease forwards;
  opacity: 0;
  transform: translateY(20px);
}

@keyframes fadeInUp {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.user-message {
  margin-left: auto;
  margin-right: 0;
  max-width: 85%;
}

.bot-message {
  margin-left: 0;
  margin-right: auto;
  max-width: 85%;
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.sender-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
  color: white;
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bot-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.sender-name {
  font-weight: 600;
  color: #333;
  font-size: 0.95rem;
}

.message-time {
  color: #999;
  font-size: 0.85rem;
  font-weight: 400;
}

.message-content {
  background: white;
  padding: 1.25rem;
  border-radius: 18px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  position: relative;
}

.user-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-message .message-content::before {
  content: '';
  position: absolute;
  top: -8px;
  right: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid #667eea;
}

.bot-message .message-content::before {
  content: '';
  position: absolute;
  top: -8px;
  left: 20px;
  width: 0;
  height: 0;
  border-left: 10px solid transparent;
  border-right: 10px solid transparent;
  border-bottom: 10px solid white;
}

.message-content p {
  margin: 0;
  line-height: 1.6;
  white-space: pre-wrap;
  word-wrap: break-word;
}

.search-highlight-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-top: 0.75rem;
  color: #667eea;
  font-size: 0.85rem;
  font-weight: 500;
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-container {
    padding: 1rem 0.5rem;
  }
  
  .history-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .history-subtitle {
    font-size: 1rem;
  }
  
  .search-input {
    padding: 0.875rem 0.875rem 0.875rem 2.5rem;
    font-size: 0.95rem;
  }
  
  .search-icon {
    left: 0.875rem;
  }
  
  .clear-search {
    right: 0.875rem;
  }
  
  .auth-prompt, .loading-container, .error-container, .empty-state, .no-results {
    padding: 2rem 1.5rem;
  }
  
  .auth-features {
    flex-direction: column;
    gap: 1rem;
  }
  
  .chat-history-list {
    padding: 1.5rem;
    max-height: 60vh;
  }
  
  .message-bubble {
    max-width: 95%;
  }
  
  .user-message, .bot-message {
    max-width: 95%;
  }
  
  .avatar {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }
  
  .sender-info {
    gap: 0.5rem;
  }
  
  .message-content {
    padding: 1rem;
  }
  
  .message-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .user-message .message-header {
    align-items: flex-end;
  }
}

@media (max-width: 480px) {
  .history-title {
    font-size: 1.75rem;
  }
  
  .auth-prompt h3, .error-container h3, .empty-state h3, .no-results h3 {
    font-size: 1.5rem;
  }
  
  .auth-prompt p, .error-container p, .empty-state p, .no-results p {
    font-size: 1rem;
  }
  
  .feature-tip {
    font-size: 0.95rem;
  }
}

/* Smooth scrolling for chat history */
.chat-history-list {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
.chat-history-list::-webkit-scrollbar {
  width: 6px;
}

.chat-history-list::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
  border-radius: 3px;
}

.chat-history-list::-webkit-scrollbar-thumb {
  background: rgba(102, 126, 234, 0.3);
  border-radius: 3px;
}

.chat-history-list::-webkit-scrollbar-thumb:hover {
  background: rgba(102, 126, 234, 0.5);
}
