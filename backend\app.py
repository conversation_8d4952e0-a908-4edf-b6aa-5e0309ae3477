from flask import Flask, request, jsonify
from flask_cors import CORS
import os
import logging
from google import genai
from supabase import create_client
from dotenv import load_dotenv

load_dotenv(dotenv_path="../.env")

# ✅ Allowed file extensions
ALLOWED_EXTENSIONS = {'pdf', 'doc', 'docx', 'png', 'jpg', 'jpeg', 'webp'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

# Logging setup
logging.basicConfig(level=logging.DEBUG)

# API and Supabase credentials (use environment variables or hardcode securely)
VITE_SUPABASE_URL = "https://bhrwvazkvsebdxstdcow.supabase.co/"
VITE_SUPABASE_KEY = os.environ.get('VITE_SUPABASE_KEY')
GEMINI_API_KEY = os.environ.get('GEMINI_API_KEY')


# Initialize Flask app
app = Flask(__name__)
CORS(app, resources={
    r"/chat": {"origins": ["http://localhost:5173", "http://localhost:5174"], "methods": ["POST"]},
    r"/upload": {"origins": ["http://localhost:5173", "http://localhost:5174"], "methods": ["POST"]},
    r"/history": {"origins": ["http://localhost:5173", "http://localhost:5174"], "methods": ["GET"]}})

# File upload config
UPLOAD_FOLDER = 'uploads'
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
os.makedirs(UPLOAD_FOLDER, exist_ok=True)  # Creates folder if not exists

# Initialize Supabase
supabase = create_client(VITE_SUPABASE_URL, VITE_SUPABASE_KEY)

# Initialize Google GenAI (Gemini) client
client = genai.Client(api_key=GEMINI_API_KEY)

# Supabase insertion function
def insert_chat_log(user_message, response_message, user_id=None):
    try:
        # Insert into chat_logs table
        chat_log_data = {
            "user_message": user_message,
            "response_message": response_message
        }
        if user_id:
            chat_log_data["user_id"] = user_id

        supabase.table("chat_logs").insert(chat_log_data).execute()

        # If user is logged in, also insert individual messages into chat_messages table
        if user_id:
            try:
                # Insert user message
                user_msg_data = {
                    "user_id": user_id,
                    "sender": "user",
                    "text": user_message
                }
                supabase.table("chat_messages").insert(user_msg_data).execute()

                # Insert bot response
                bot_msg_data = {
                    "user_id": user_id,
                    "sender": "bot",
                    "text": response_message
                }
                supabase.table("chat_messages").insert(bot_msg_data).execute()
                logging.info("Messages successfully inserted into both tables.")
            except Exception as e:
                logging.error(f"Error inserting into chat_messages: {str(e)}")
        else:
            logging.info("Message inserted into chat_logs only (no user logged in).")

    except Exception as e:
        logging.error(f"Error during Supabase insertion: {str(e)}")

# Google Gemini (GenAI) request function
def genai_query(user_message):
    try:
        # Call the Google Gemini model to generate content
        response = client.models.generate_content(
            model="gemini-2.5-flash",  # Or another supported model
            contents=user_message
        )

        # Access the generated text
        return response.text.strip()

    except Exception as e:
        logging.error(f"Error in Gemini request: {str(e)}")
        return None

# Chat endpoint
@app.route('/chat', methods=['POST'])
def chat():
    try:
        user_message = request.json.get("message", "")
        user_id = request.json.get("user_id", None)  # Get user_id from request

        if not user_message:
            return jsonify({"error": "No message provided"}), 400

        logging.info(f"Received message: {user_message} from user: {user_id}")

        # Send the message to Google Gemini
        reply = genai_query(user_message)

        if not reply:
            return jsonify({"error": "No response from Google Gemini."}), 500

        logging.info(f"Generated reply: {reply}")

        # Log to Supabase with user_id
        insert_chat_log(user_message, reply, user_id)

        return jsonify({"response": reply}), 200

    except Exception as e:
        logging.error(f"Error in /chat endpoint: {str(e)}")
        return jsonify({"error": "Something went wrong. Please try again later."}), 500

@app.route('/upload', methods=['POST'])
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part"}), 400

    file = request.files['file']

    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if file and allowed_file(file.filename):
        file_path = os.path.join(app.config['UPLOAD_FOLDER'], file.filename)
        file.save(file_path)
        logging.info(f"Uploaded file saved at: {file_path}")
        return jsonify({"message": "File uploaded successfully", "filename": file.filename}), 200

    return jsonify({"error": "Invalid file type"}), 400

# History endpoint
@app.route('/history', methods=['GET'])
def get_history():
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({"error": "User ID is required"}), 400

        logging.info(f"Fetching history for user: {user_id}")

        # Fetch chat history from chat_logs table
        response = supabase.table("chat_logs").select("*").eq("user_id", user_id).order("created_at", desc=False).execute()

        if response.data:
            # Format the data for frontend consumption
            formatted_history = []
            for log in response.data:
                # Add user message
                if log.get('user_message'):
                    formatted_history.append({
                        'id': f"{log['id']}_user",
                        'sender': 'user',
                        'text': log['user_message'],
                        'timestamp': log['created_at']
                    })
                # Add bot response
                if log.get('response_message'):
                    formatted_history.append({
                        'id': f"{log['id']}_bot",
                        'sender': 'bot',
                        'text': log['response_message'],
                        'timestamp': log['created_at']
                    })

            return jsonify({"history": formatted_history}), 200
        else:
            return jsonify({"history": []}), 200

    except Exception as e:
        logging.error(f"Error in /history endpoint: {str(e)}")
        return jsonify({"error": "Something went wrong while fetching history."}), 500

# Enhanced conversations endpoint with better grouping
@app.route('/conversations', methods=['GET'])
def get_conversations():
    try:
        user_id = request.args.get('user_id')
        if not user_id:
            return jsonify({"error": "User ID is required"}), 400

        logging.info(f"Fetching conversations for user: {user_id}")

        # Fetch chat history from chat_logs table ordered by creation time
        response = supabase.table("chat_logs").select("*").eq("user_id", user_id).order("created_at", desc=False).execute()

        if response.data:
            conversations = []
            current_conversation = None
            conversation_id = 1

            for log in response.data:
                # Create conversation entry
                log_time = log['created_at']

                # Group conversations by time gaps (1 hour = 3600 seconds)
                if (current_conversation is None or
                    (log_time and current_conversation['last_activity'] and
                     abs(parse_timestamp(log_time) - parse_timestamp(current_conversation['last_activity'])) > 3600)):

                    # Start new conversation
                    if current_conversation:
                        conversations.append(current_conversation)

                    # Generate conversation title from first user message
                    title = generate_conversation_title(log.get('user_message', ''))

                    current_conversation = {
                        'id': f"conv_{conversation_id}",
                        'title': title,
                        'messages': [],
                        'message_count': 0,
                        'created_at': log_time,
                        'last_activity': log_time
                    }
                    conversation_id += 1

                # Add messages to current conversation
                if log.get('user_message'):
                    current_conversation['messages'].append({
                        'id': f"{log['id']}_user",
                        'sender': 'user',
                        'text': log['user_message'],
                        'timestamp': log_time
                    })
                    current_conversation['message_count'] += 1

                if log.get('response_message'):
                    current_conversation['messages'].append({
                        'id': f"{log['id']}_bot",
                        'sender': 'bot',
                        'text': log['response_message'],
                        'timestamp': log_time
                    })
                    current_conversation['message_count'] += 1

                # Update last activity
                current_conversation['last_activity'] = log_time

            # Add the last conversation
            if current_conversation:
                conversations.append(current_conversation)

            # Reverse to show most recent first
            conversations.reverse()

            return jsonify({"conversations": conversations}), 200
        else:
            return jsonify({"conversations": []}), 200

    except Exception as e:
        logging.error(f"Error in /conversations endpoint: {str(e)}")
        return jsonify({"error": "Something went wrong while fetching conversations."}), 500

def parse_timestamp(timestamp_str):
    """Parse timestamp string to seconds since epoch"""
    try:
        from datetime import datetime
        # Handle different timestamp formats
        if 'T' in timestamp_str:
            dt = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
        else:
            dt = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')
        return dt.timestamp()
    except:
        return 0

def generate_conversation_title(first_message):
    """Generate a meaningful conversation title from the first message"""
    if not first_message:
        return "New Conversation"

    import re

    # Remove common question words and clean up
    clean_message = re.sub(r'^(what|how|why|when|where|who|can|could|would|should|is|are|do|does|did|tell|explain|help)\s+', '', first_message, flags=re.IGNORECASE)
    clean_message = re.sub(r'\?+$', '', clean_message).strip()

    # Truncate if too long
    if len(clean_message) > 40:
        clean_message = clean_message[:40].strip() + '...'

    return clean_message if clean_message else "New Conversation"

# Run app
if __name__ == '__main__':
    app.run(debug=True)