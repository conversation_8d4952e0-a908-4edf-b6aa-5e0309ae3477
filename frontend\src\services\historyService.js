/**
 * Fetches chat history for a given user from the backend API.
 * @param {string} userId
 * @returns {Promise<Array<{ id: string, sender: string, text: string, timestamp: string }>>}
 */
export async function fetchChatHistory(userId) {
  try {
    const response = await fetch(`http://127.0.0.1:5000/history?user_id=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.history || [];
  } catch (error) {
    console.error("Error fetching chat history:", error.message);
    return [];
  }
}

/**
 * Fetches grouped conversations for a given user from the backend API.
 * @param {string} userId
 * @returns {Promise<Array<{ id: string, title: string, messages: Array, message_count: number, created_at: string, last_activity: string }>>}
 */
export async function fetchConversations(userId) {
  try {
    const response = await fetch(`http://127.0.0.1:5000/conversations?user_id=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.conversations || [];
  } catch (error) {
    console.error("Error fetching conversations:", error.message);
    return [];
  }
}
