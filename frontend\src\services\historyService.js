/**
 * Fetches chat history for a given user from the backend API.
 * @param {string} userId
 * @returns {Promise<Array<{ id: string, sender: string, text: string, timestamp: string }>>}
 */
export async function fetchChatHistory(userId) {
  try {
    console.log("fetchChatHistory called with userId:", userId);
    const url = `http://127.0.0.1:5000/history?user_id=${userId}`;
    console.log("Making request to:", url);

    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    console.log("Response status:", response.status);
    console.log("Response ok:", response.ok);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Error response:", errorText);
      throw new Error(`HTTP error! status: ${response.status}, message: ${errorText}`);
    }

    const data = await response.json();
    console.log("Response data:", data);
    return data.history || [];
  } catch (error) {
    console.error("Error fetching chat history:", error.message);
    console.error("Full error:", error);
    return [];
  }
}

/**
 * Fetches grouped conversations for a given user from the backend API.
 * @param {string} userId
 * @returns {Promise<Array<{ id: string, title: string, messages: Array, message_count: number, created_at: string, last_activity: string }>>}
 */
export async function fetchConversations(userId) {
  try {
    const response = await fetch(`http://127.0.0.1:5000/conversations?user_id=${userId}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    return data.conversations || [];
  } catch (error) {
    console.error("Error fetching conversations:", error.message);
    return [];
  }
}
