import { createClient } from "@supabase/supabase-js";

const supabase = createClient(
  import.meta.env.VITE_SUPABASE_URL,
  import.meta.env.VITE_SUPABASE_KEY
);

/**
 * Upserts the authenticated user into your `users` table
 * @param {import('@supabase/supabase-js').Session} session
 */
export async function upsertUser(session) {
  if (!session?.user) return;
  const { id, email, user_metadata } = session.user;

  const { error } = await supabase
    .from("users")
    .upsert({
      user_id: id,
      email,
      name: user_metadata?.full_name ?? user_metadata?.name ?? null,
    });
  if (error) console.error("Upsert user error:", error.message);
}
