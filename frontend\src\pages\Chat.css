/* Modern Responsive Chat Layout */
.chat-app {
  height: 100vh;
  overflow: hidden;
  background: url('../assets/hero_bg.png') center / cover no-repeat,
              radial-gradient(circle at bottom, #EDB437 -90%, #060606 80%);
  background-blend-mode: overlay;
  background-repeat: no-repeat;
  background-position: center center;
}

/* Mobile Navigation */
.mobile-nav {
  display: flex;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--cs-border);
  padding: 0.75rem 1rem;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.nav-btn {
  flex: 1;
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--cs-white);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.25rem;
  margin-right: 0.5rem;
  position: relative;
  overflow: hidden;
}

.nav-btn:last-child {
  margin-right: 0;
}

.nav-btn.active {
  background: var(--cs-primary);
  color: var(--cs-black);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(237, 180, 55, 0.4);
}

.nav-btn i {
  font-size: 1.1rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.sidebar-toggle {
  background: rgba(237, 180, 55, 0.1);
  border: 1px solid var(--cs-border);
  padding: 0.75rem;
  border-radius: 15px;
  color: var(--cs-white);
  margin-left: 0.5rem;
  transition: all 0.3s ease;
  min-width: 48px;
  font-weight: 600;
}

.sidebar-toggle:hover {
  background: var(--cs-primary);
  color: var(--cs-black);
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(237, 180, 55, 0.3);
}

/* Desktop Layout */
.desktop-layout {
  height: 100vh;
  width: 100%;
}

.sidebar-section {
  width: 280px;
  flex-shrink: 0;
  background: rgba(23, 23, 23, 0.8);
  backdrop-filter: blur(10px);
  border-right: 1px solid var(--cs-border);
  overflow-y: auto;
  box-shadow: 2px 0 20px rgba(0, 0, 0, 0.3);
}

.chat-section {
  flex: 1;
  background: transparent;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.history-section {
  width: 400px;
  flex-shrink: 0;
  background: transparent;
  border-left: 1px solid var(--cs-border);
  overflow: hidden;
}

/* Mobile Layout */
.mobile-layout {
  height: 100vh;
  padding-top: 80px; /* Account for mobile nav */
  overflow: hidden;
}

.mobile-chat,
.mobile-history {
  height: 100%;
  overflow: hidden;
}

/* Mobile Sidebar Overlay */
.mobile-sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(6, 6, 6, 0.7);
  backdrop-filter: blur(5px);
  z-index: 2000;
  display: flex;
  animation: fadeIn 0.3s ease;
}

.mobile-sidebar {
  width: 280px;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  height: 100%;
  overflow-y: auto;
  transform: translateX(-100%);
  animation: slideIn 0.3s ease forwards;
  box-shadow: 4px 0 30px rgba(0, 0, 0, 0.5);
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideIn {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

/* History Component Mobile Overrides */
@media (max-width: 767.98px) {
  .mobile-history .history-container {
    height: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .mobile-history .history-header {
    flex-shrink: 0;
    margin-bottom: 1rem;
  }
  
  .mobile-history .history-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }
  
  .mobile-history .chat-sessions-list {
    flex: 1;
    margin: 0;
    max-height: none;
    height: 100%;
    overflow-y: auto;
    padding: 1rem;
  }
  
  .mobile-history .session-card {
    margin-bottom: 1rem;
    padding: 1rem;
  }
  
  .mobile-history .session-header {
    gap: 0.75rem;
  }
  
  .mobile-history .session-icon {
    width: 40px;
    height: 40px;
    font-size: 1rem;
  }
  
  .mobile-history .session-title {
    font-size: 1rem;
    line-height: 1.3;
  }
  
  .mobile-history .session-preview {
    font-size: 0.9rem;
    line-height: 1.4;
  }
}

/* Tablet Adjustments */
@media (min-width: 768px) and (max-width: 1199.98px) {
  .history-section {
    width: 350px;
  }
  
  .sidebar-section {
    width: 250px;
  }
}

/* Large Desktop */
@media (min-width: 1400px) {
  .history-section {
    width: 450px;
  }
  
  .sidebar-section {
    width: 320px;
  }
}

/* Smooth transitions */
.chat-section,
.history-section,
.sidebar-section {
  transition: all 0.3s ease;
}

/* Focus states for accessibility */
.nav-btn:focus,
.sidebar-toggle:focus {
  outline: 2px solid var(--cs-primary);
  outline-offset: 2px;
  box-shadow: 0 0 0 4px rgba(237, 180, 55, 0.3);
}

/* Loading states */
.mobile-layout.loading {
  opacity: 0.7;
  pointer-events: none;
}

/* Landscape mobile optimization */
@media (max-width: 767.98px) and (orientation: landscape) {
  .mobile-nav {
    padding: 0.5rem 1rem;
  }
  
  .nav-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
  
  .nav-btn i {
    font-size: 1rem;
  }
  
  .mobile-layout {
    padding-top: 70px;
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .mobile-nav {
    border-bottom-width: 0.5px;
  }
  
  .sidebar-section,
  .history-section {
    border-width: 0.5px;
  }
}

/* Dark mode support (already using dark theme) */
@media (prefers-color-scheme: light) {
  .chat-app {
    background: url('../assets/hero_bg.png') center / cover no-repeat,
                radial-gradient(circle at bottom, #EDB437 -90%, #ffffff 80%);
    background-blend-mode: overlay;
  }

  .mobile-nav {
    background: rgba(255, 255, 255, 0.95);
    border-bottom-color: var(--cs-border);
  }

  .nav-btn {
    color: var(--cs-black);
  }

  .nav-btn.active {
    background: var(--cs-primary);
    color: var(--cs-black);
  }

  .sidebar-toggle {
    background: rgba(237, 180, 55, 0.1);
    color: var(--cs-black);
  }

  .sidebar-toggle:hover {
    background: var(--cs-primary);
    color: var(--cs-black);
  }

  .desktop-layout .sidebar-section {
    background: rgba(255, 255, 255, 0.9);
    border-color: var(--cs-border);
  }
}

/* ChatGPT-style Layout */
.chatgpt-layout {
  display: flex;
  width: 100%;
  height: 100vh;
}

.history-sidebar {
  width: 280px;
  height: 100vh;
  flex-shrink: 0;
  border-right: 1px solid var(--cs-border);
}

.main-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.chat-top-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  background: rgba(23, 23, 23, 0.8);
  backdrop-filter: blur(15px);
  border-bottom: 1px solid var(--cs-border);
  flex-shrink: 0;
}

.history-toggle-btn {
  background: none;
  border: none;
  color: var(--cs-white);
  font-size: 1.1rem;
  padding: 0.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.history-toggle-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: var(--cs-primary);
}

.chat-title {
  color: var(--cs-white);
  font-size: 1.1rem;
  font-weight: 600;
  flex: 1;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 0 1rem;
}

.new-chat-btn {
  background: var(--cs-primary);
  border: none;
  color: var(--cs-black);
  font-size: 0.9rem;
  font-weight: 600;
  padding: 0.5rem 1rem;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.new-chat-btn:hover {
  background: var(--cs-hover);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(237, 180, 55, 0.3);
}

.chat-content {
  flex: 1;
  overflow: hidden;
}

/* Mobile History Overlay */
.mobile-history-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: none;
}

.mobile-history-sidebar {
  width: 280px;
  height: 100%;
  background: rgba(23, 23, 23, 0.95);
  backdrop-filter: blur(15px);
  transform: translateX(-100%);
  transition: transform 0.3s ease;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .history-sidebar {
    display: none;
  }

  .mobile-history-overlay {
    display: block;
  }

  .mobile-history-sidebar {
    transform: translateX(0);
  }

  .chat-top-bar {
    padding: 0.75rem 1rem;
  }

  .chat-title {
    font-size: 1rem;
  }

  .new-chat-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.85rem;
  }
}

/* Print styles */
@media print {
  .history-sidebar,
  .chat-top-bar {
    display: none !important;
  }

  .main-chat-area {
    height: auto !important;
  }
}
